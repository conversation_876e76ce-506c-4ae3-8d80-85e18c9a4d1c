server {
    listen       80;
    server_name  localhost;
    client_max_body_size 100M;

    location / {
        proxy_set_header   X-Forwarded-For $remote_addr;
        proxy_set_header   Host $http_host;
        proxy_pass         http://scd-wp:80/;
    }

    location /adminer {
        proxy_set_header   X-Forwarded-For $remote_addr;
        proxy_set_header   Host $http_host;
        proxy_pass         http://scd-adminer:8080/;
    }
}
