#!/bin/bash

# Root directory
cd "$( dirname "$( realpath "$0" )" )/.." || exit 1

# Variables
bold=$(tput bold)
normal=$(tput sgr0)

src="./src/"
out="./build/"

# Clear everything in out directory except .gitignore
rm -rf "$out"* || exit "$?"

# Languages
# wp i18n make-pot "./$plugin/" "./$plugin/languages/$plugin.pot" || exit "$?"

# Define other procedures before the build
# ...

# Build
cd "$src" && npm run build && cd '../' || exit "$?"

rsync -av --exclude={.[!.]*,node_modules,vendor,bud.config.js,package*,yarn.lock,vite.config.ts,tsconfig.json,dist} "./src/" "$out" || exit "$?"

# Skopírujte build výstupy
if [ -d "./src/dist" ]; then
    cp -r "./src/dist/"* "$out/" || exit "$?"
fi

echo "${bold} The build has been successful ${normal}"

# If is called with "stage" argument, deploy to the production server
if [ "$1" == "stage" ]; then
    echo "${bold} Deploying to the stage server ${normal}"
    #rsync -avz --delete --exclude=vendor -e 'ssh -p 25649' "$out" "[SSH]/sub/stage/wp-content/themes/[name]-theme/" || exit "$?"
fi

# Deploy to the staging if no argument is provided
if [ "$1" == "" ]; then
    echo "${bold} Deploying to the staging server ${normal}"
    #rsync -avz --delete --exclude=vendor -e 'ssh -p 29567' "$out" "[SSH]/sub/kvetinaren/wp-content/themes/[name]-theme/" || exit "$?"
fi
