import { defineConfig } from "vite";
import tailwindcss from "@tailwindcss/vite";
import { resolve } from "path";

export default defineConfig({
  plugins: [tailwindcss()],
  server: {
    host: true,
    hmr: {
      port: 24680, // Použiť špecifický port pre HMR
    },
    watch: {
      usePolling: true, // Pomôže s file watching v Docker prostredí
      interval: 1000, // Polling interval v milisekundách
      ignored: ["**/node_modules/**", "**/dist/**"],
    },
    proxy: {
      // Proxy pre WordPress API
      "/wp-json": {
        target: "http://localhost:8005",
        changeOrigin: true,
        secure: false,
      },
      // Proxy pre WordPress admin
      "/wp-admin": {
        target: "http://localhost:8005",
        changeOrigin: true,
        secure: false,
      },
      // Proxy pre WordPress content
      "/wp-content": {
        target: "http://localhost:8005",
        changeOrigin: true,
        secure: false,
      },
      // Proxy pre WordPress includes
      "/wp-includes": {
        target: "http://localhost:8005",
        changeOrigin: true,
        secure: false,
      },
      // Proxy pre WordPress root a ostatné cesty (ale nie Vite assets)
      "^(?!/@vite|/node_modules|/src/|/__vite_ping|/favicon.ico).*": {
        target: "http://localhost:8005",
        changeOrigin: true,
        secure: false,
      },
    },
  },
  build: {
    // Definujte entry pointy pre CSS a JS
    rollupOptions: {
      input: {
        main: resolve(__dirname, "main.js"),
        app: resolve(__dirname, "css/app.css"),
      },
      output: {
        // Výstupné súbory do css/ a js/ priečinkov
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split(".");
          const extType = info[info.length - 1];
          if (/\.(css)$/.test(assetInfo.name)) {
            return `css/[name][extname]`;
          }
          return `assets/[name]-[hash][extname]`;
        },
        chunkFileNames: "js/[name]-[hash].js",
        entryFileNames: "js/[name].js",
      },
    },
    outDir: "dist",
    emptyOutDir: true,
  },
  css: {
    postcss: {
      plugins: [],
    },
  },
});
