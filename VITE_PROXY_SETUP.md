# Vite Proxy Setup pre WordPress Docker Environment

## Prehľad

Vite development server je teraz nak<PERSON> s proxy nasta<PERSON><PERSON><PERSON>, k<PERSON><PERSON> um<PERSON><PERSON> bezprob<PERSON><PERSON>v<PERSON> pr<PERSON>cu s WordPress Docker environmentom bežiacim na `http://localhost:8005`.

## Konfigurácia

V súbore `src/vite.config.ts` boli pridané nasledujúce proxy nastavenia:

```typescript
server: {
  proxy: {
    // Proxy všetky požiadavky okrem Vite assets na WordPress Docker server
    '^(?!/(@vite|node_modules|src)/).*': {
      target: 'http://localhost:8005',
      changeOrigin: true,
      secure: false,
      ws: true, // WebSocket support
    },
    // Špecifické proxy pravidlá pre WordPress cesty
    '/wp-json': { target: 'http://localhost:8005', changeOrigin: true, secure: false },
    '/wp-admin': { target: 'http://localhost:8005', changeOrigin: true, secure: false },
    '/wp-content': { target: 'http://localhost:8005', changeOrigin: true, secure: false },
    '/wp-includes': { target: 'http://localhost:8005', changeOrigin: true, secure: false }
  }
}
```

## Ako to funguje

1. **Docker Environment**: WordPress beží v Docker kontajneroch a je dostupný cez nginx proxy na `localhost:8005`
2. **Vite Dev Server**: Keď spustíte `npm run dev`, Vite server sa spustí (obvykle na porte 5173)
3. **Proxy Routing**: Všetky požiadavky (okrem Vite-špecifických súborov) sa automaticky presmerujú na WordPress Docker server
4. **Live Reloading**: Vite stále poskytuje hot module replacement pre CSS a JS súbory

## Použitie

### Spustenie development servera

```bash
cd src
npm run dev
```

### Workflow

1. Uistite sa, že Docker environment beží: `make start` (z root adresára)
2. Spustite Vite dev server: `npm run dev` (z src adresára)
3. Otvorte prehliadač na adrese, ktorú zobrazí Vite (obvykle `http://localhost:5173`)
4. Všetky WordPress požiadavky sa automaticky presmerujú na `localhost:8005`
5. CSS zmeny sa aplikujú okamžite bez obnovenia stránky

### Výhody

- **Hot Module Replacement**: Okamžité aplikovanie CSS zmien
- **Rýchly development**: Vite poskytuje rýchly build a reload
- **Bezproblémová integrácia**: Funguje s existujúcim WordPress Docker setupom
- **WebSocket podpora**: Pre live reloading a HMR

## Poznámky

- Proxy funguje len v development móde (`npm run dev`)
- Build proces (`npm run build`) zostáva nezmenený
- Všetky WordPress funkcionality (admin, API, content) fungujú normálne cez proxy
